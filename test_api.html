<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>会计科目API测试</h1>
    <button onclick="testAPI()">测试API</button>
    <div id="result"></div>

    <script>
    function testAPI() {
        console.log('开始测试API...');
        
        $.ajax({
            url: 'http://127.0.0.1:8080/financial/accounting-subjects/api?include_system=true',
            method: 'GET',
            dataType: 'json',
            success: function(data) {
                console.log('API成功:', data);
                document.getElementById('result').innerHTML = 
                    '<h3>API调用成功</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            },
            error: function(xhr, status, error) {
                console.error('API失败:', {status, error, xhr});
                document.getElementById('result').innerHTML = 
                    '<h3>API调用失败</h3><p>Status: ' + status + '</p><p>Error: ' + error + '</p><p>Response: ' + xhr.responseText + '</p>';
            }
        });
    }
    </script>
</body>
</html>
