{% extends "financial/base.html" %}

{% block page_title %}
{% if subject %}编辑会计科目{% else %}新增会计科目{% endif %}
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ url_for('financial.accounting_subjects_index') }}">会计科目管理</a></li>
<li class="breadcrumb-item active">
    {% if subject %}编辑科目{% else %}新增科目{% endif %}
</li>
{% endblock %}

{% block page_actions %}
<div class="financial-actions">
    <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
    </a>
</div>
{% endblock %}

{% block financial_content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-{% if subject %}edit{% else %}plus{% endif %}"></i>
                {% if subject %}编辑会计科目{% else %}新增会计科目{% endif %}
            </div>
            <div class="financial-card-body">
                <form method="POST" class="financial-form">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.code.label(class="form-label") }}
                                {{ form.code(class="form-control" + (" is-invalid" if form.code.errors else "")) }}
                                {% if form.code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.code.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    科目编码用于唯一标识会计科目，建议使用数字编码
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.subject_type.label(class="form-label") }}
                                {{ form.subject_type(class="form-control" + (" is-invalid" if form.subject_type.errors else ""), id="subject_type") }}
                                {% if form.subject_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.subject_type.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.balance_direction.label(class="form-label") }}
                                {{ form.balance_direction(class="form-control" + (" is-invalid" if form.balance_direction.errors else ""), id="balance_direction") }}
                                {% if form.balance_direction.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.balance_direction.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted" id="balance_direction_help">
                                    余额方向将根据科目类型自动设置
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 上级科目选择 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-sitemap"></i> 上级科目
                            <small class="text-muted">（可选）</small>
                        </label>

                        <div class="alert alert-info mb-3">
                            <i class="fas fa-lightbulb"></i>
                            <strong>创建规则：</strong>选择上级科目可以在现有科目下创建子科目，不选则创建独立的一级科目
                        </div>

                        <!-- 科目树 -->
                        <div class="border rounded" style="background-color: #f8f9fa;">
                            <div class="p-3" style="max-height: 400px; overflow-y: auto;">
                                <div id="subject-tree-list">
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                                        <p class="mt-2">加载科目数据中...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{ form.parent_id(style="display: none;") }}

                        <!-- 选中的上级科目显示 -->
                        <div id="selected-parent" class="mt-3" style="display: none;">
                            <div class="alert alert-success">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong><i class="fas fa-check-circle"></i> 已选择上级科目：</strong>
                                        <span id="selected-parent-text" class="ml-2"></span>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="clearParentSelection()">
                                        <i class="fas fa-times"></i> 取消选择
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 创建模式提示 -->
                        <div id="create-mode-tip" class="mt-2">
                            <div class="alert alert-secondary">
                                <i class="fas fa-plus-circle"></i>
                                <strong>当前模式：</strong>创建独立的一级科目
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            可选，用于说明科目的用途和核算内容
                        </small>
                    </div>
                    
                    <div class="form-group text-center">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-secondary btn-lg">取消</a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 使用指南 -->
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-question-circle"></i> 创建会计科目指南
            </div>
            <div class="financial-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-plus-circle text-primary"></i> 两种创建方式</h6>
                        <div class="mb-3">
                            <div class="border-left border-primary pl-3">
                                <strong>1. 创建子科目</strong>
                                <p class="small text-muted mb-1">选择上级科目 → 在现有科目下创建子科目</p>
                                <ul class="small">
                                    <li>自动继承上级科目的类型和余额方向</li>
                                    <li>自动生成子科目编码</li>
                                    <li>适合精细化管理</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="border-left border-success pl-3">
                                <strong>2. 创建独立科目</strong>
                                <p class="small text-muted mb-1">不选上级科目 → 创建独立的一级科目</p>
                                <ul class="small">
                                    <li>需要手动选择科目类型</li>
                                    <li>系统自动设置余额方向</li>
                                    <li>适合全新业务</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-lightbulb text-warning"></i> 推荐扩展示例</h6>
                        <div class="small">
                            <strong>📊 主营业务收入 → 细分收入：</strong>
                            <ul class="mb-2">
                                <li>学生早餐收入、学生午餐收入</li>
                                <li>教师餐费收入、外来人员餐费</li>
                            </ul>

                            <strong>📦 库存商品 → 食材分类：</strong>
                            <ul class="mb-2">
                                <li>米面粮油、新鲜蔬菜</li>
                                <li>肉禽蛋类、调料用品</li>
                            </ul>

                            <strong>💰 管理费用 → 费用细分：</strong>
                            <ul class="mb-2">
                                <li>水电燃气费、厨师工资</li>
                                <li>设备维护费、清洁用品费</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-magic"></i>
                    <strong>智能功能：</strong>
                    系统会根据您的选择自动生成科目编码、设置余额方向，让创建科目变得简单快捷！
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_css %}
<style>
/* 科目树样式优化 */
.hover-bg-light:hover {
    background-color: #f8f9fa !important;
}

.subject-item .d-flex.bg-primary {
    background-color: #007bff !important;
    color: white !important;
}

.subject-item .d-flex.bg-primary code {
    color: white !important;
    background: none !important;
}

.subject-item .d-flex.bg-primary .badge {
    background-color: rgba(255,255,255,0.2) !important;
    color: white !important;
}

.expand-icon {
    transition: transform 0.2s ease;
}

.expand-icon.expanded {
    transform: rotate(90deg);
}

/* 科目类型标题样式 */
.text-primary.border-bottom {
    border-color: #dee2e6 !important;
}

/* 加载动画优化 */
.fa-spin {
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 表单样式优化 */
.financial-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.financial-form .alert {
    border: none;
    border-radius: 8px;
}

.financial-card {
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.financial-card-header {
    border-radius: 12px 12px 0 0;
}
</style>
{% endblock %}

{% block financial_js %}
<script>
// 会计科目表单页面特定JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化...');

    const subjectTypeSelect = document.getElementById('subject_type');
    const balanceDirectionSelect = document.getElementById('balance_direction');
    const balanceDirectionHelp = document.getElementById('balance_direction_help');
    const codeInput = document.getElementById('code');
    const nameInput = document.getElementById('name');
    const parentIdInput = document.getElementById('parent_id');
    const subjectTreeList = document.getElementById('subject-tree-list');
    const selectedParent = document.getElementById('selected-parent');
    const selectedParentText = document.getElementById('selected-parent-text');
    const createModeTip = document.getElementById('create-mode-tip');

    // 检查关键元素是否存在
    console.log('关键元素检查:');
    console.log('subjectTreeList:', subjectTreeList);
    console.log('subjectTypeSelect:', subjectTypeSelect);
    console.log('parentIdInput:', parentIdInput);

    if (!subjectTreeList) {
        console.error('找不到 subject-tree-list 元素！');
        return;
    }

    // 科目类型与余额方向的对应关系
    const balanceDirectionMap = {
        '资产': '借方',
        '费用': '借方',
        '负债': '贷方',
        '所有者权益': '贷方',
        '收入': '贷方'
    };

    // 科目类型说明
    const subjectTypeDescriptions = {
        '资产': '资产类科目余额方向为借方，用于核算学校食堂拥有的各种资产',
        '负债': '负债类科目余额方向为贷方，用于核算学校食堂的各种债务',
        '所有者权益': '所有者权益类科目余额方向为贷方，用于核算学校食堂的净资产',
        '收入': '收入类科目余额方向为贷方，用于核算学校食堂的各种收入',
        '费用': '费用类科目余额方向为借方，用于核算学校食堂的各种支出'
    };

    // 存储所有科目数据
    let allSubjects = [];
    let selectedParentId = null;

    // 加载所有科目数据
    function loadAllSubjects() {
        console.log('开始加载科目数据...');

        // 显示加载状态
        subjectTreeList.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                <p class="text-muted">正在加载科目数据...</p>
            </div>
        `;

        // 使用jQuery的AJAX请求
        $.ajax({
            url: '/financial/accounting-subjects/api?include_system=true',
            method: 'GET',
            dataType: 'json',
            timeout: 10000, // 10秒超时
            success: function(data) {
                console.log('API响应成功，数据长度:', data.length);
                console.log('前3条数据:', data.slice(0, 3));
                allSubjects = data || [];
                buildSubjectTree();
            },
            error: function(xhr, status, error) {
                console.error('加载科目数据失败:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText,
                    statusCode: xhr.status
                });
                subjectTreeList.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                        <p class="text-danger">加载失败: ${error} (${xhr.status})</p>
                        <button type="button" class="btn btn-primary" onclick="loadAllSubjects()">
                            <i class="fas fa-redo"></i> 重试
                        </button>
                    </div>
                `;
            }
        });
    }

    // 构建科目树
    function buildSubjectTree() {
        console.log('构建科目树开始，科目数量:', allSubjects.length);
        console.log('allSubjects类型:', typeof allSubjects);
        console.log('allSubjects是否为数组:', Array.isArray(allSubjects));

        if (!allSubjects || !Array.isArray(allSubjects) || allSubjects.length === 0) {
            console.log('没有科目数据，显示初始化按钮');
            subjectTreeList.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无会计科目数据</p>
                    <button type="button" class="btn btn-primary" onclick="initSystemSubjects()">
                        <i class="fas fa-plus"></i> 初始化系统科目
                    </button>
                </div>
            `;
            return;
        }

        // 按科目类型分组
        const groupedSubjects = {};
        allSubjects.forEach(subject => {
            if (!groupedSubjects[subject.subject_type]) {
                groupedSubjects[subject.subject_type] = [];
            }
            groupedSubjects[subject.subject_type].push(subject);
        });

        console.log('科目分组:', groupedSubjects);

        let html = '';
        const typeOrder = ['资产', '负债', '净资产', '所有者权益', '收入', '费用'];
        console.log('科目类型顺序:', typeOrder);

        typeOrder.forEach(type => {
            if (groupedSubjects[type] && groupedSubjects[type].length > 0) {
                html += `<div class="mb-3">`;
                html += `<h6 class="text-primary border-bottom pb-2"><i class="fas fa-folder"></i> ${type}类科目</h6>`;

                // 只显示一级科目，点击后可以展开子科目
                const topLevelSubjects = groupedSubjects[type].filter(s => s.level === 1);
                topLevelSubjects.forEach(subject => {
                    html += buildSubjectItem(subject, groupedSubjects[type]);
                });

                html += `</div>`;
            }
        });

        if (html === '') {
            html = '<div class="text-muted text-center py-3">没有找到可用的科目数据</div>';
        }

        subjectTreeList.innerHTML = html;
        console.log('科目树构建完成');
    }

    // 构建单个科目项
    function buildSubjectItem(subject, allTypeSubjects) {
        const badge = subject.is_system ?
            '<span class="badge badge-success badge-sm ml-2">系统</span>' :
            '<span class="badge badge-primary badge-sm ml-2">学校</span>';

        const children = allTypeSubjects.filter(s => s.parent_id === subject.id);
        const hasChildren = children.length > 0;
        const expandIcon = hasChildren ?
            '<i class="fas fa-chevron-right text-muted"></i>' :
            '<i class="fas fa-circle text-muted" style="font-size: 6px;"></i>';

        const indent = (subject.level - 1) * 20;

        let html = `
            <div class="subject-item" data-id="${subject.id}" data-level="${subject.level}">
                <div class="d-flex align-items-center p-2 mb-1 border rounded hover-bg-light"
                     style="cursor: pointer; margin-left: ${indent}px; transition: background-color 0.2s;">
                    <span class="expand-icon mr-2 d-flex align-items-center justify-content-center"
                          style="width: 16px; height: 16px;">${expandIcon}</span>
                    <code class="mr-2 text-dark" style="background: none; font-size: 0.9em;">${subject.code}</code>
                    <span class="flex-grow-1">${subject.name}</span>
                    ${badge}
                </div>
                <div class="children" style="display: none;">
        `;

        // 递归添加子科目
        children.forEach(child => {
            html += buildSubjectItem(child, allTypeSubjects);
        });

        html += `</div></div>`;
        return html;
    }

    // 科目选择事件
    document.addEventListener('click', function(e) {
        const subjectItem = e.target.closest('.subject-item .d-flex');
        if (subjectItem) {
            const subjectDiv = subjectItem.closest('.subject-item');
            const subjectId = subjectDiv.dataset.id;
            const subject = allSubjects.find(s => s.id == subjectId);

            if (subject) {
                // 清除之前的选中状态
                document.querySelectorAll('.subject-item .d-flex').forEach(item => {
                    item.classList.remove('bg-primary', 'text-white');
                });

                // 设置新的选中状态
                subjectItem.classList.add('bg-primary', 'text-white');

                // 更新选中信息
                selectedParentId = subjectId;
                parentIdInput.value = subjectId;
                selectedParentText.textContent = `${subject.code} - ${subject.name}`;
                selectedParent.style.display = 'block';
                createModeTip.style.display = 'none';

                // 自动设置科目类型和余额方向（继承上级科目）
                subjectTypeSelect.value = subject.subject_type;
                balanceDirectionSelect.value = subject.balance_direction;
                balanceDirectionHelp.textContent = `将在 "${subject.name}" 下创建子科目，自动继承科目类型和余额方向`;

                // 生成子科目编码建议
                generateChildCode(subject);
            }
        }

        // 展开/折叠处理
        const expandIcon = e.target.closest('.expand-icon');
        if (expandIcon) {
            e.stopPropagation();
            const subjectDiv = expandIcon.closest('.subject-item');
            const childrenDiv = subjectDiv.querySelector('.children');
            const icon = expandIcon.querySelector('i');

            if (childrenDiv && childrenDiv.children.length > 0) {
                if (childrenDiv.style.display === 'none') {
                    childrenDiv.style.display = 'block';
                    icon.className = 'fas fa-chevron-down text-muted';
                    expandIcon.classList.add('expanded');
                } else {
                    childrenDiv.style.display = 'none';
                    icon.className = 'fas fa-chevron-right text-muted';
                    expandIcon.classList.remove('expanded');
                }
            }
        }
    });

    // 生成子科目编码
    function generateChildCode(parentSubject) {
        const existingCodes = allSubjects
            .filter(s => s.parent_id == parentSubject.id)
            .map(s => s.code)
            .sort();

        for (let i = 1; i <= 999; i++) {
            const code = parentSubject.code + i.toString().padStart(3, '0');
            if (!existingCodes.includes(code)) {
                codeInput.value = code;
                codeInput.style.borderColor = '#28a745';
                break;
            }
        }
    }

    // 清除上级科目选择
    window.clearParentSelection = function() {
        selectedParentId = null;
        parentIdInput.value = '';
        selectedParent.style.display = 'none';
        createModeTip.style.display = 'block';

        // 清除选中状态
        document.querySelectorAll('.subject-item .d-flex').forEach(item => {
            item.classList.remove('bg-primary', 'text-white');
        });

        // 重置为独立科目模式
        balanceDirectionHelp.textContent = '请选择科目类型，系统将自动设置余额方向';
        generateIndependentCode();
    };

    // 生成独立科目编码
    function generateIndependentCode() {
        const selectedType = subjectTypeSelect.value;
        if (!selectedType) return;

        const typePrefix = {
            '资产': '1',
            '负债': '2',
            '所有者权益': '3',
            '收入': '6',
            '费用': '6'
        };

        const prefix = typePrefix[selectedType];
        if (prefix) {
            const existingCodes = allSubjects
                .filter(s => s.subject_type === selectedType && s.level === 1)
                .map(s => s.code)
                .sort();

            for (let i = 1; i <= 999; i++) {
                const code = prefix + i.toString().padStart(3, '0');
                if (!existingCodes.includes(code)) {
                    codeInput.value = code;
                    codeInput.style.borderColor = '#28a745';
                    break;
                }
            }
        }
    }

    // 科目类型变化时自动设置余额方向
    function updateBalanceDirection() {
        const selectedType = subjectTypeSelect.value;

        if (selectedParentId) {
            // 如果已选择上级科目，不允许修改科目类型
            const parentSubject = allSubjects.find(s => s.id == selectedParentId);
            if (parentSubject && selectedType !== parentSubject.subject_type) {
                alert('子科目必须与上级科目保持相同的科目类型');
                subjectTypeSelect.value = parentSubject.subject_type;
                return;
            }
        } else {
            // 独立科目模式：根据科目类型设置余额方向
            if (selectedType && balanceDirectionMap[selectedType]) {
                balanceDirectionSelect.value = balanceDirectionMap[selectedType];
                balanceDirectionHelp.textContent = `${subjectTypeDescriptions[selectedType]}`;
                generateIndependentCode();
            } else {
                balanceDirectionHelp.textContent = '请选择科目类型，系统将自动设置余额方向';
            }
        }
    }

    // 监听科目类型变化
    subjectTypeSelect.addEventListener('change', updateBalanceDirection);

    // 初始化系统科目
    window.initSystemSubjects = function() {
        if (!confirm('确定要初始化系统会计科目吗？这将创建标准的会计科目体系。')) {
            return;
        }

        subjectTreeList.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                <p class="text-muted">正在初始化系统科目...</p>
            </div>
        `;

        $.ajax({
            url: '/financial/accounting-subjects/init-system',
            method: 'POST',
            dataType: 'json',
            headers: {
                'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
            },
            success: function(result) {
                if (result.success) {
                    alert('系统科目初始化成功！');
                    // 重新加载科目数据
                    loadAllSubjects();
                } else {
                    alert('初始化失败：' + result.message);
                    subjectTreeList.innerHTML = `
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                            <p class="text-muted">初始化失败</p>
                            <button type="button" class="btn btn-primary" onclick="initSystemSubjects()">
                                <i class="fas fa-redo"></i> 重试
                            </button>
                        </div>
                    `;
                }
            },
            error: function(xhr, status, error) {
                console.error('初始化系统科目失败:', status, error);
                alert('初始化失败，请重试');
                subjectTreeList.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                        <p class="text-muted">网络错误</p>
                        <button type="button" class="btn btn-primary" onclick="initSystemSubjects()">
                            <i class="fas fa-redo"></i> 重试
                        </button>
                    </div>
                `;
            }
        });
    };

    // 将loadAllSubjects设为全局函数，以便重试按钮调用
    window.loadAllSubjects = loadAllSubjects;

    // 初始化
    loadAllSubjects();
    updateBalanceDirection();

    // 表单验证
    const form = document.querySelector('.financial-form');
    form.addEventListener('submit', function(e) {
        const code = codeInput.value.trim();
        const name = nameInput.value.trim();

        if (!code) {
            alert('请输入科目编码');
            e.preventDefault();
            return false;
        }

        if (!name) {
            alert('请输入科目名称');
            e.preventDefault();
            return false;
        }

        // 检查科目编码是否重复
        const existingSubject = allSubjects.find(s => s.code === code);
        if (existingSubject) {
            alert(`科目编码 ${code} 已被使用：${existingSubject.name}`);
            e.preventDefault();
            return false;
        }

        // 显示提交状态
        const submitBtn = form.querySelector('input[type="submit"]');
        if (typeof showLoading === 'function') {
            showLoading(submitBtn);
        }
    });

    // 实时编码验证
    codeInput.addEventListener('input', function() {
        const code = this.value.trim();
        if (code) {
            const existingSubject = allSubjects.find(s => s.code === code);
            if (existingSubject) {
                this.style.borderColor = '#dc3545';
                this.title = `编码已被使用：${existingSubject.name}`;
            } else {
                this.style.borderColor = '#28a745';
                this.title = '编码可用';
            }
        } else {
            this.style.borderColor = '';
            this.title = '';
        }
    });
});
</script>
{% endblock %}
