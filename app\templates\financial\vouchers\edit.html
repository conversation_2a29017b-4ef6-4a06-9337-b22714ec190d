{% extends "financial/base.html" %}

{% block page_title %}编辑财务凭证{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ url_for('financial.vouchers_index') }}">财务凭证管理</a></li>
<li class="breadcrumb-item active">编辑凭证</li>
{% endblock %}

{% block page_actions %}
<div class="financial-actions">
    <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="btn btn-info">
        <i class="fas fa-eye"></i> 查看详情
    </a>
    <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
    </a>
</div>
{% endblock %}

{% block financial_content %}
<div class="row">
    <div class="col-lg-12">
        <!-- 凭证基本信息 -->
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-edit"></i> 凭证基本信息
                <span class="badge badge-{{ 'warning' if voucher.status == '草稿' else 'info' }} ml-2">
                    {{ voucher.status }}
                </span>
            </div>
            <div class="financial-card-body">
                {% if form.errors %}
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> 表单验证失败</h6>
                    <ul class="mb-0">
                        {% for field_name, errors in form.errors.items() %}
                            {% for error in errors %}
                            <li><strong>{{ field_name }}:</strong> {{ error }}</li>
                            {% endfor %}
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}

                <form method="POST" class="financial-form" id="voucherForm">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">凭证号</label>
                                <input type="text" class="form-control" value="{{ voucher.voucher_number }}" readonly>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.voucher_date.label(class="form-label") }}
                                {{ form.voucher_date(class="form-control" + (" is-invalid" if form.voucher_date.errors else "")) }}
                                {% if form.voucher_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.voucher_date.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.voucher_type.label(class="form-label") }}
                                {{ form.voucher_type(class="form-control" + (" is-invalid" if form.voucher_type.errors else "")) }}
                                {% if form.voucher_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.voucher_type.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">凭证金额</label>
                                <input type="text" class="form-control financial-amount"
                                       value="{{ voucher.total_amount }}" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.summary.label(class="form-label") }}
                                {{ form.summary(class="form-control" + (" is-invalid" if form.summary.errors else "")) }}
                                {% if form.summary.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.summary.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    <span id="summaryCounter">0/200 字符</span>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="3") }}
                                {% if form.notes.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.notes.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save"></i> 更新凭证
                        </button>
                        <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 凭证明细 -->
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-list"></i> 凭证明细
                <div class="float-right">
                    <button type="button" class="btn btn-success btn-sm" id="addDetailBtn" onclick="$('#detailModal').modal('show')">
                        <i class="fas fa-plus"></i> 添加明细
                    </button>
                </div>
            </div>
            <div class="financial-card-body">
                <div class="table-responsive">
                    <table class="table financial-table" id="detailsTable">
                        <thead>
                            <tr>
                                <th width="8%">行号</th>
                                <th width="25%">会计科目</th>
                                <th width="25%">摘要</th>
                                <th width="12%">借方金额</th>
                                <th width="12%">贷方金额</th>
                                <th width="10%">辅助信息</th>
                                <th width="8%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for detail in details %}
                            <tr data-detail-id="{{ detail.id }}">
                                <td>{{ detail.line_number }}</td>
                                <td>{{ detail.subject.code }} - {{ detail.subject.name }}</td>
                                <td>{{ detail.summary }}</td>
                                <td class="text-right">
                                    {% if detail.debit_amount > 0 %}
                                        <span class="financial-amount">{{ "%.2f"|format(detail.debit_amount) }}</span>
                                    {% endif %}
                                </td>
                                <td class="text-right">
                                    {% if detail.credit_amount > 0 %}
                                        <span class="financial-amount">{{ "%.2f"|format(detail.credit_amount) }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ detail.auxiliary_info or '' }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-warning btn-sm edit-detail-btn"
                                                data-detail-id="{{ detail.id }}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm delete-detail-btn"
                                                data-detail-id="{{ detail.id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                            {% if not details %}
                            <tr id="noDetailsRow">
                                <td colspan="7" class="text-center text-muted">
                                    暂无明细记录
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                        <tfoot>
                            <tr class="table-info">
                                <th colspan="3">合计</th>
                                <th class="text-right" id="totalDebit">0.00</th>
                                <th class="text-right" id="totalCredit">0.00</th>
                                <th colspan="2"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加/编辑明细模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel">添加凭证明细</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- 添加加载指示器 -->
                <div id="loadingIndicator" class="text-center" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载数据，请稍候...</p>
                </div>

                <form id="detailForm">
                    <!-- 会计科目级联选择 -->
                    <div class="form-group">
                        <label class="form-label">会计科目选择</label>

                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">科目类型</label>
                                <select class="form-control" id="subjectType" name="subject_type">
                                    <option value="">请选择科目类型...</option>
                                    <option value="资产">资产类科目</option>
                                    <option value="负债">负债类科目</option>
                                    <option value="净资产">净资产类科目</option>
                                    <option value="收入">收入类科目</option>
                                    <option value="费用">费用类科目</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">大类科目</label>
                                <select class="form-control" id="majorSubject" name="major_subject_id" disabled>
                                    <option value="">请先选择科目类型...</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">一级科目</label>
                                <select class="form-control" id="firstLevelSubject" name="first_level_subject_id" disabled>
                                    <option value="">请先选择大类科目...</option>
                                </select>
                            </div>
                        </div>

                        <!-- 明细科目级联选择器 -->
                        <div class="form-group mt-3">
                            <label class="form-label">明细科目</label>
                            <div class="cascader-container" id="detailSubjectCascader" style="display: none;">
                                <!-- 级联菜单将在此处动态生成 -->
                            </div>
                            <input type="hidden" id="finalSubjectId" name="subject_id" required>
                            <div class="invalid-feedback mt-2">请选择明细科目</div>
                        </div>

                        <!-- 选择完成提示 -->
                        <div id="selectionComplete" style="display: none; margin-top: 10px;">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                已选择科目：<span id="selectedSubjectDisplay"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="form-label">摘要 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="detailSummary" name="summary"
                                       placeholder="请输入摘要" maxlength="200" required>
                                <small class="form-text text-muted">
                                    <span id="detailSummaryCounter">0/200 字符</span>
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">借方金额</label>
                                <input type="number" class="form-control" id="debitAmount" name="debit_amount"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">贷方金额</label>
                                <input type="number" class="form-control" id="creditAmount" name="credit_amount"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">辅助信息</label>
                        <input type="text" class="form-control" id="auxiliaryInfo" name="auxiliary_info"
                               placeholder="可选，用于补充说明" maxlength="200">
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>提示：</strong>借方金额和贷方金额必须填写其中一个，且不能同时填写。
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveDetailBtn">
                    <i class="fas fa-save"></i> 保存明细
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}

<script>
// 全局变量
let currentEditingDetailId = null;
let subjectSelectionState = {
    selectedType: null,
    selectedMajor: null,
    selectedFirstLevel: null,
    selectedFinalSubject: null,
    isLoading: false,
    isSubmitting: false
};

// 封装通用的数据获取函数
function fetchData(url, data, successCallback, errorCallback, completeCallback, context) {
    setLoading(true);
    $.ajax({
        url: url,
        data: data,
        timeout: 5000, // 默认5秒超时
        success: function(response) {
            if (successCallback) {
                successCallback(response);
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error(`[${context}] 加载数据失败:`, textStatus, errorThrown);
            showToast('error', `加载数据失败: ${textStatus}，请重试`);
            logError(errorThrown, context);
            if (errorCallback) {
                errorCallback(jqXHR, textStatus, errorThrown);
            }
        },
        complete: function() {
            setLoading(false);
            if (completeCallback) {
                completeCallback();
            }
        }
    });
}

// 初始化页面
function initVoucherEditPage() {
    console.log('开始初始化财务凭证编辑页面...');

    // 初始化摘要字符计数
    initSummaryCounter();

    // 初始化明细表格合计
    updateDetailTotals();

    // 初始化表单验证
    initFormValidation();

    // 绑定事件
    bindEvents();

    console.log('财务凭证编辑页面初始化完成');
}

// 初始化摘要字符计数
function initSummaryCounter() {
    const summaryField = document.querySelector('input[name="summary"]');
    const counter = document.getElementById('summaryCounter');

    if (summaryField && counter) {
        function updateCounter() {
            const length = summaryField.value.length;
            counter.textContent = `${length}/200 字符`;
            if (length > 200) {
                counter.className = 'form-text text-danger';
            } else if (length > 180) {
                counter.className = 'form-text text-warning';
            } else {
                counter.className = 'form-text text-muted';
            }
        }

        summaryField.addEventListener('input', updateCounter);
        updateCounter();
    }

    // 明细摘要字符计数
    const detailSummaryField = document.getElementById('detailSummary');
    const detailCounter = document.getElementById('detailSummaryCounter');

    if (detailSummaryField && detailCounter) {
        function updateDetailCounter() {
            const length = detailSummaryField.value.length;
            detailCounter.textContent = `${length}/200 字符`;
            if (length > 200) {
                detailCounter.className = 'form-text text-danger';
            } else if (length > 180) {
                detailCounter.className = 'form-text text-warning';
            } else {
                detailCounter.className = 'form-text text-muted';
            }
        }

        detailSummaryField.addEventListener('input', updateDetailCounter);
        updateDetailCounter();
    }
}

// 更新明细合计
function updateDetailTotals() {
    let totalDebit = 0;
    let totalCredit = 0;

    document.querySelectorAll('#detailsTable tbody tr[data-detail-id]').forEach(row => {
        const debitCell = row.querySelector('td:nth-child(4) .financial-amount');
        const creditCell = row.querySelector('td:nth-child(5) .financial-amount');

        if (debitCell) {
            totalDebit += parseFloat(debitCell.textContent) || 0;
        }
        if (creditCell) {
            totalCredit += parseFloat(creditCell.textContent) || 0;
        }
    });

    document.getElementById('totalDebit').textContent = totalDebit.toFixed(2);
    document.getElementById('totalCredit').textContent = totalCredit.toFixed(2);
}

// 初始化表单验证
function initFormValidation() {
    const voucherForm = document.getElementById('voucherForm');
    if (voucherForm) {
        voucherForm.addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;

            // 显示提交状态
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 更新中...';
            submitBtn.disabled = true;

            // 基本验证
            const voucherDate = document.querySelector('input[name="voucher_date"]').value;
            const voucherType = document.querySelector('select[name="voucher_type"]').value;
            const summary = document.querySelector('input[name="summary"]').value;

            if (!voucherDate || !voucherType || !summary.trim()) {
                e.preventDefault();
                alert('请填写所有必填字段：凭证日期、凭证类型、摘要');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                return false;
            }

            if (summary.length > 200) {
                e.preventDefault();
                alert('摘要长度不能超过200个字符');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                return false;
            }
        });
    }
}

// 简单的事件绑定
function bindEvents() {
    // 添加明细按钮
    const addBtn = document.getElementById('addDetailBtn');
    if (addBtn) {
        addBtn.onclick = showAddDetailModal;
    }

    // 保存明细按钮
    const saveBtn = document.getElementById('saveDetailBtn');
    if (saveBtn) {
        saveBtn.onclick = saveDetail;
    }

    // 编辑和删除按钮（事件委托）
    document.onclick = function(e) {
        // 编辑明细
        if (e.target.closest('.edit-detail-btn')) {
            const detailId = e.target.closest('.edit-detail-btn').dataset.detailId;
            editDetail(parseInt(detailId));
            return;
        }

        // 删除明细
        if (e.target.closest('.delete-detail-btn')) {
            const detailId = e.target.closest('.delete-detail-btn').dataset.detailId;
            deleteDetail(parseInt(detailId));
            return;
        }
    };

    // 科目类型级联选择事件
    $('#subjectType').change(function() {
        const selectedType = $(this).val();
        subjectSelectionState.selectedType = selectedType;
        if (selectedType) {
            loadMajorSubjects(selectedType);
        } else {
            resetSelect('#majorSubject', '请先选择科目类型...');
            resetSelect('#firstLevelSubject', '请先选择大类科目...');
            $('#detailSubjectCascader').hide();
            $('#selectionComplete').hide();
            subjectSelectionState.selectedMajor = null;
            subjectSelectionState.selectedFirstLevel = null;
            subjectSelectionState.selectedFinalSubject = null;
            updateSelectedDetailSubject();
        }
    });

    // 大类科目变更事件
    $('#majorSubject').change(function() {
        const selectedMajor = $(this).val();
        subjectSelectionState.selectedMajor = selectedMajor;
        if (selectedMajor) {
            loadFirstLevelSubjects(selectedMajor);
        } else {
            resetSelect('#firstLevelSubject', '请先选择大类科目...');
            $('#detailSubjectCascader').hide();
            $('#selectionComplete').hide();
            subjectSelectionState.selectedFirstLevel = null;
            subjectSelectionState.selectedFinalSubject = null;
            updateSelectedDetailSubject();
        }
    });

    // 一级科目变更事件
    $('#firstLevelSubject').change(function() {
        const selectedFirstLevel = $(this).val();
        subjectSelectionState.selectedFirstLevel = selectedFirstLevel;
        if (selectedFirstLevel) {
            buildCascaderMenu(selectedFirstLevel);
            $('#detailSubjectCascader').show();
        } else {
            $('#detailSubjectCascader').hide();
            $('#selectionComplete').hide();
            subjectSelectionState.selectedFinalSubject = null;
            updateSelectedDetailSubject();
        }
    });

    // 借贷金额互斥
    const debitAmount = document.getElementById('debitAmount');
    if (debitAmount) {
        debitAmount.oninput = function() {
            if (this.value && parseFloat(this.value) > 0) {
                document.getElementById('creditAmount').value = '';
            }
        };
    }

    const creditAmount = document.getElementById('creditAmount');
    if (creditAmount) {
        creditAmount.oninput = function() {
            if (this.value && parseFloat(this.value) > 0) {
                document.getElementById('debitAmount').value = '';
            }
        };
    }
}

// 设置加载状态
function setLoading(isLoading) {
    subjectSelectionState.isLoading = isLoading;
    const container = $('#detailSubjectCascader');
    
    if (isLoading) {
        if (!container.find('.loading-overlay').length) {
            container.append(`
                <div class="loading-overlay">
                    <div class="loading-spinner"></div>
                </div>
            `);
        }
        container.find('.loading-overlay').show();
    } else {
        container.find('.loading-overlay').hide();
    }
    
    $('#detailForm').find('input, select, button').not('#saveDetailBtn').prop('disabled', isLoading);
    if (isLoading) {
        $('#saveDetailBtn').prop('disabled', true);
    } else if (!subjectSelectionState.isSubmitting) {
        $('#saveDetailBtn').prop('disabled', false);
    }
}

// 错误日志记录
function logError(error, context) {
    console.error(`[${context}]`, error);
    // 发送到服务器日志
    $.post('/api/log/error', {
        context: context,
        error: error.toString(),
        stack: error.stack
    });
}

// 加载大类科目
function loadMajorSubjects(subjectType) {
    resetSelect('#majorSubject', '加载中...');
    resetSelect('#firstLevelSubject', '请先选择大类科目...');
    $('#detailSubjectCascader').hide();
    $('#selectionComplete').hide();
    subjectSelectionState.selectedMajor = null;
    subjectSelectionState.selectedFirstLevel = null;
    subjectSelectionState.selectedFinalSubject = null;
    updateSelectedDetailSubject();

    fetchData(
        '{{ url_for("financial.accounting_subjects_by_type") }}',
        { subject_type: subjectType },
        function(data) {
            if (data && data.length > 0) {
                fillSelectOptions('#majorSubject', data, '请选择大类科目...');
                $('#majorSubject').prop('disabled', false);
            } else {
                resetSelect('#majorSubject', '该类型暂无科目');
            }
        },
        function(jqXHR, textStatus, errorThrown) {
            resetSelect('#majorSubject', `加载失败: ${textStatus}`);
        },
        null,
        'loadMajorSubjects'
    );
}

// 加载一级科目
function loadFirstLevelSubjects(majorSubjectId) {
    resetSelect('#firstLevelSubject', '加载中...');
    $('#detailSubjectCascader').hide();
    $('#selectionComplete').hide();
    subjectSelectionState.selectedFirstLevel = null;
    subjectSelectionState.selectedFinalSubject = null;
    updateSelectedDetailSubject();

    fetchData(
        '{{ url_for("financial.accounting_subjects_by_parent") }}',
        { parent_id: majorSubjectId },
        function(data) {
            if (!data || data.length === 0) {
                // 如果没有二级科目，直接使用大类科目作为最终选择
                const majorText = $('#majorSubject option:selected').text();
                fillSelectOptions('#firstLevelSubject', [{id: majorSubjectId, display_name: majorText}], majorText);
                subjectSelectionState.selectedFirstLevel = majorSubjectId;
                subjectSelectionState.selectedFinalSubject = majorSubjectId;
                $('#selectedSubjectDisplay').text(majorText);
                $('#selectionComplete').show();
                $('#firstLevelSubject').prop('disabled', true); // Disable if no children
            } else {
                fillSelectOptions('#firstLevelSubject', data, '请选择一级科目...');
                $('#firstLevelSubject').prop('disabled', false);
            }
        },
        function(jqXHR, textStatus, errorThrown) {
            resetSelect('#firstLevelSubject', `加载失败: ${textStatus}`);
        },
        null,
        'loadFirstLevelSubjects'
    );
}

// 显示添加明细模态框
function showAddDetailModal() {
    console.log('显示添加明细模态框...');

    currentEditingDetailId = null;
    document.getElementById('detailModalLabel').textContent = '添加凭证明细';

    // 重置表单
    resetDetailForm();

    // 显示模态框
    $('#detailModal').modal('show');
}

// 编辑明细
function editDetail(detailId) {
    currentEditingDetailId = detailId;
    document.getElementById('detailModalLabel').textContent = '编辑凭证明细';

    // 获取明细数据
    const row = document.querySelector(`tr[data-detail-id="${detailId}"]`);
    if (!row) {
        alert('找不到明细记录');
        return;
    }

    // 从表格行中提取数据
    const subjectText = row.querySelector('td:nth-child(2)').textContent.trim();
    const summary = row.querySelector('td:nth-child(3)').textContent.trim();
    const debitAmountElement = row.querySelector('td:nth-child(4) .financial-amount');
    const creditAmountElement = row.querySelector('td:nth-child(5) .financial-amount');
    const auxiliaryInfo = row.querySelector('td:nth-child(6)').textContent.trim();

    const debitAmount = debitAmountElement ? parseFloat(debitAmountElement.textContent) : 0;
    const creditAmount = creditAmountElement ? parseFloat(creditAmountElement.textContent) : 0;

    // 重置表单
    resetDetailForm();

    // 填充表单数据
    document.getElementById('detailSummary').value = summary;
    document.getElementById('debitAmount').value = debitAmount > 0 ? debitAmount : '';
    document.getElementById('creditAmount').value = creditAmount > 0 ? creditAmount : '';
    document.getElementById('auxiliaryInfo').value = auxiliaryInfo;

    // TODO: 这里需要根据科目信息回显科目选择 - 复杂，暂只显示文本
    document.getElementById('selectionComplete').style.display = 'block';
    document.getElementById('selectedSubjectDisplay').textContent = subjectText;
    // 由于后端API只返回直接子级，回显复杂多级选择需要多次API调用或传递完整科目树

    // 显示模态框
    $('#detailModal').modal('show');
}

// 删除明细
function deleteDetail(detailId) {
    if (!confirm('确定要删除这条明细记录吗？')) {
        return;
    }

    const voucherId = parseInt('{{ voucher.id }}');

    fetch(`{{ url_for('financial.delete_voucher_detail', voucher_id=0, detail_id=0) }}`.replace('0/details/0', `${voucherId}/details/${detailId}`), {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 移除表格行
            const row = document.querySelector(`tr[data-detail-id="${detailId}"]`);
            if (row) {
                row.remove();
            }

            // 检查是否还有明细
            const remainingRows = document.querySelectorAll('#detailsTable tbody tr[data-detail-id]');
            if (remainingRows.length === 0) {
                // 显示无明细提示
                const tbody = document.querySelector('#detailsTable tbody');
                tbody.innerHTML = `
                    <tr id="noDetailsRow">
                        <td colspan="7" class="text-center text-muted">暂无明细记录</td>
                    </tr>
                `;
            }

            // 更新合计
            updateDetailTotals();

            // 显示成功消息
            showToast('success', '明细删除成功');
        } else {
            showToast('error', '删除失败：' + data.message);
            logError(new Error(data.message), 'deleteDetail');
        }
    })
    .catch(error => {
        console.error('删除明细失败:', error);
        showToast('error', '删除失败，请重试');
        logError(error, 'deleteDetail');
    });
}

// 保存明细
function saveDetail() {
    if (subjectSelectionState.isSubmitting) {
        return;
    }

    // 验证表单
    if (!validateDetailForm()) {
        return;
    }

    subjectSelectionState.isSubmitting = true;
    setLoading(true);

    const formData = getDetailFormData();
    const voucherId = parseInt('{{ voucher.id }}');

    const url = currentEditingDetailId
        ? `{{ url_for('financial.update_voucher_detail', voucher_id=0, detail_id=0) }}`.replace('0/details/0', `${voucherId}/details/${currentEditingDetailId}`)
        : `{{ url_for('financial.add_voucher_detail', voucher_id=0) }}`.replace('/details', `/${voucherId}/details`);

    const method = currentEditingDetailId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#detailModal').modal('hide');
            location.reload();
        } else {
            showToast('error', '保存失败：' + data.message);
            logError(new Error(data.message), 'saveDetail');
        }
    })
    .catch(error => {
        console.error('保存明细失败:', error);
        showToast('error', '保存失败，请重试');
        logError(error, 'saveDetail');
    })
    .finally(() => {
        subjectSelectionState.isSubmitting = false;
        setLoading(false);
    });
}

// 验证明细表单
function validateDetailForm() {
    const subjectId = $('#finalSubjectId').val();
    const summary = document.getElementById('detailSummary').value.trim();
    const debitAmount = parseFloat(document.getElementById('debitAmount').value) || 0;
    const creditAmount = parseFloat(document.getElementById('creditAmount').value) || 0;

    if (!subjectId) {
        showToast('error', '请选择会计科目');
        return false;
    }

    if (!summary) {
        showToast('error', '请输入摘要');
        document.getElementById('detailSummary').focus();
        return false;
    }

    if (summary.length > 200) {
        showToast('error', '摘要长度不能超过200个字符');
        document.getElementById('detailSummary').focus();
        return false;
    }

    if (debitAmount === 0 && creditAmount === 0) {
        showToast('error', '借方金额和贷方金额必须填写其中一个');
        return false;
    }

    if (debitAmount > 0 && creditAmount > 0) {
        showToast('error', '借方金额和贷方金额不能同时填写');
        return false;
    }

    if (debitAmount < 0 || creditAmount < 0) {
        showToast('error', '金额不能为负数');
        return false;
    }

    // 验证金额精度
    const amount = debitAmount || creditAmount;
    if (amount.toString().split('.')[1]?.length > 2) {
        showToast('error', '金额最多支持两位小数');
        return false;
    }

    return true;
}

// 获取明细表单数据
function getDetailFormData() {
    return {
        subject_id: parseInt($('#finalSubjectId').val()),
        summary: document.getElementById('detailSummary').value.trim(),
        debit_amount: parseFloat(document.getElementById('debitAmount').value) || 0,
        credit_amount: parseFloat(document.getElementById('creditAmount').value) || 0,
        auxiliary_info: document.getElementById('auxiliaryInfo').value.trim()
    };
}

// 重置明细表单
function resetDetailForm() {
    subjectSelectionState = {
        selectedType: null,
        selectedMajor: null,
        selectedFirstLevel: null,
        selectedFinalSubject: null,
        isLoading: false,
        isSubmitting: false
    };
    document.getElementById('detailForm').reset();
    resetSelect('#subjectType', '请选择科目类型...');
    resetSelect('#majorSubject', '请先选择科目类型...');
    resetSelect('#firstLevelSubject', '请先选择大类科目...');
    $('#detailSubjectCascader').hide();
    $('#selectionComplete').hide();
    $('#loadingIndicator').hide();
    $('#detailForm').find('input, select, button').prop('disabled', false);

    // 清空所有级联菜单
    $(".cascader-menu").empty();
    updateSelectedDetailSubject();
}

// 显示提示消息
function showToast(type, message) {
    // 简单的提示实现，可以根据项目需要使用 toastr 或其他库
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    // 在页面顶部显示提示
    const container = document.querySelector('.financial-content') || document.body;
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}

// 重置下拉选择框
function resetSelect(selector, placeholderText) {
    $(selector)
        .html(`<option value="">${placeholderText}</option>`)
        .prop("disabled", true);
}

// 填充下拉选择框选项
function fillSelectOptions(selector, items, placeholderText) {
    let options = `<option value="">${placeholderText}</option>`;
    if (items && items.length > 0) {
        items.forEach(item => {
            options += `<option value="${item.id}">${item.display_name || item.name}</option>`;
        });
    }
    $(selector).html(options);
}

// ======================= 级联菜单相关函数 =======================
// 构建级联菜单 (第三级及更深)
function buildCascaderMenu(parentId) {
    // 清空所有级联菜单
    $("#detailSubjectCascader").empty(); // 清空容器内所有动态生成的菜单
    $('#selectionComplete').hide();
    subjectSelectionState.selectedFinalSubject = null;
    updateSelectedDetailSubject(); // Clear hidden input

    // 获取第一级明细科目（即选定的一级科目下的直接子级）
    fetchData(
        '{{ url_for("financial.accounting_subjects_by_parent") }}',
        { parent_id: parentId },
        function(data) {
            if (data && data.length > 0) {
                renderCascaderMenu(data, 1); // 渲染第一级菜单
                $('#detailSubjectCascader').show(); // 显示容器
            } else {
                // 如果没有下级科目，则将当前选中的一级科目作为最终科目
                const selectedText = $('#firstLevelSubject option:selected').text();
                subjectSelectionState.selectedFinalSubject = parentId;
                $('#selectedSubjectDisplay').text(selectedText);
                $('#selectionComplete').show();
                updateSelectedDetailSubject();
                $('#detailSubjectCascader').hide(); // 隐藏容器
            }
        },
        function(jqXHR, textStatus, errorThrown) {
            // Error handled by fetchData
            $('#detailSubjectCascader').hide(); // 隐藏容器
        },
        null,
        'buildCascaderMenu_initial'
    );
}

// 渲染级联菜单项
function renderCascaderMenu(items, level) {
    const menuId = `detailSubjectMenu${level}`;
    let menu = $('#' + menuId);
    
    if (menu.length === 0) {
        menu = $(`<div class="cascader-menu" id="${menuId}"></div>`);
        $('#detailSubjectCascader').append(menu);
    } else {
        menu.empty();
    }
    
    menu.show();

    items.forEach(item => {
        const hasChildren = item.has_children || false;
        const itemElement = $(`
            <div class="cascader-item ${hasChildren ? 'has-children' : ''}" 
                 data-id="${item.id}" 
                 data-name="${item.display_name || item.name}"
                 data-code="${item.code || ''}">
                <span class="subject-code">${item.code || ''}</span>
                <span class="subject-name">${item.display_name || item.name}</span>
                ${hasChildren ? '<i class="fas fa-angle-right"></i>' : ''}
            </div>
        `);

        menu.append(itemElement);

        itemElement.on("click", function() {
            const currentId = $(this).data("id");
            const currentName = $(this).data("name");
            const currentCode = $(this).data("code");

            // 选中当前项
            $(this).addClass("selected").siblings().removeClass("selected");

            // 移除并隐藏后续所有菜单
            $('.cascader-menu').filter(function() {
                return parseInt($(this).attr('id').replace('detailSubjectMenu', '')) > level;
            }).remove();

            subjectSelectionState.selectedFinalSubject = currentId;
            $('#selectedSubjectDisplay').html(`
                <span class="subject-code">${currentCode}</span>
                <span class="subject-name">${currentName}</span>
            `);
            $('#selectionComplete').show();
            updateSelectedDetailSubject();

            if (hasChildren) {
                setLoading(true);
                fetchData(
                    '{{ url_for("financial.accounting_subjects_by_parent") }}',
                    { parent_id: currentId },
                    function(childrenData) {
                        if (childrenData && childrenData.length > 0) {
                            renderCascaderMenu(childrenData, level + 1);
                        }
                    },
                    function(jqXHR, textStatus, errorThrown) {
                        showToast('error', '加载子科目失败，请重试');
                    },
                    function() {
                        setLoading(false);
                    },
                    'renderCascaderMenu_children'
                );
            }
        });
    });
}

// 更新选中的明细科目
function updateSelectedDetailSubject() {
    const finalSelectedId = subjectSelectionState.selectedFinalSubject;
    const finalSelectedDisplay = $('#selectedSubjectDisplay').html();

    $("#finalSubjectId").val(finalSelectedId || '');

    if (finalSelectedId) {
        $('#selectionComplete').html(`
            <div class="alert alert-info mb-0">
                <i class="fas fa-check-circle"></i>
                已选择科目：<span class="selected-subject">${finalSelectedDisplay}</span>
            </div>
        `).show();
        $("#finalSubjectId").closest(".form-group").find(".invalid-feedback").hide();
    } else {
        $('#selectionComplete').hide();
        $("#finalSubjectId").closest(".form-group").find(".invalid-feedback").show();
    }
}

// 添加级联菜单样式
$(document.head).append(`
    <style>
        .cascader-container {
            display: flex;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            min-height: 38px;
            overflow: hidden;
            background: #fff;
            position: relative;
        }
        .cascader-menu {
            flex: 1;
            min-width: 200px;
            max-height: 300px;
            overflow-y: auto;
            border-right: 1px solid #eee;
            padding: 8px 0;
            display: none;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .cascader-menu:last-child {
            border-right: none;
        }
        .cascader-item {
            padding: 8px 16px;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .cascader-item:hover {
            background-color: #f8f9fa;
        }
        .cascader-item.selected {
            background-color: #e3f2fd;
            color: #1976d2;
            font-weight: 500;
        }
        .cascader-item .fa-angle-right {
            color: #999;
            transition: transform 0.2s ease;
        }
        .cascader-item:hover .fa-angle-right {
            color: #666;
        }
        .cascader-item.selected .fa-angle-right {
            color: #1976d2;
        }
        .cascader-item.has-children .fa-angle-right {
            display: inline-block;
        }
        .cascader-item.has-children:hover .fa-angle-right {
            transform: translateX(2px);
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .loading-spinner {
            width: 24px;
            height: 24px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .subject-code {
            color: #666;
            font-size: 0.9em;
            margin-right: 8px;
        }
        .subject-name {
            flex: 1;
        }
        .selection-complete {
            margin-top: 12px;
            padding: 12px;
            border-radius: 4px;
            background: #e3f2fd;
            border: 1px solid #bbdefb;
        }
        .selection-complete .selected-subject {
            color: #1976d2;
            font-weight: 500;
        }
    </style>
`);

// 财务凭证编辑页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initVoucherEditPage();

    // 添加调试按钮（仅在开发环境）
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const debugBtn = document.createElement('button');
        debugBtn.textContent = '调试会计科目API';
        debugBtn.className = 'btn btn-warning btn-sm';
        debugBtn.style.position = 'fixed';
        debugBtn.style.top = '10px';
        debugBtn.style.right = '10px';
        debugBtn.style.zIndex = '9999';
        debugBtn.onclick = debugAccountingSubjects;
        document.body.appendChild(debugBtn);
    }
});
</script>
{% endblock %}